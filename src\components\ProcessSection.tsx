
import { Search, CheckCircle, Truck, ShoppingBag } from "lucide-react";

const processSteps = [
  {
    icon: <Search className="h-10 w-10 text-white" />,
    title: "Sourcing",
    description: "We source quality second-hand clothing from trusted suppliers worldwide."
  },
  {
    icon: <CheckCircle className="h-10 w-10 text-white" />,
    title: "Quality Check",
    description: "Every item is carefully inspected, cleaned, and graded for quality before sale."
  },
  {
    icon: <Truck className="h-10 w-10 text-white" />,
    title: "Delivery",
    description: "Items are delivered to our stores and made available for purchase at great prices."
  },
  {
    icon: <ShoppingBag className="h-10 w-10 text-white" />,
    title: "Happy Shopping",
    description: "Customers enjoy quality fashion at unbeatable prices with excellent service."
  }
];

const ProcessSection = () => {
  return (
    <section className="section bg-secondary/20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-medium mb-4">How M-Thrift Works</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Our streamlined process ensures you get the best quality thrift clothing at unbeatable prices, every time.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {processSteps.map((step, index) => (
          <div key={index} className="flex flex-col h-full">
            <div className="bg-primary rounded-full h-20 w-20 flex items-center justify-center mb-6">
              {step.icon}
            </div>
            <h3 className="text-xl font-medium mb-3">{step.title}</h3>
            <p className="text-muted-foreground flex-grow">{step.description}</p>
            
            {index < processSteps.length - 1 && (
              <div className="hidden lg:block h-px w-full bg-primary/30 relative mt-6">
                <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 h-3 w-3 rounded-full bg-primary"></div>
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-20">
        <div className="rounded-lg overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2">
            <div className="bg-primary text-white p-10 md:p-16 flex flex-col justify-center">
              <h3 className="text-2xl md:text-3xl font-medium mb-6">Visit Our Stores</h3>
              <p className="mb-6">
                Experience M-Thrift in person! Visit any of our three Nairobi locations to browse our full collection.
                Our friendly staff will help you find exactly what you're looking for.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a href="#" className="btn-primary inline-block text-center bg-white text-primary hover:bg-white/90">Find Locations</a>
                <a href="#" className="btn-outline inline-block text-center border-white text-white hover:bg-white/20">Contact Us</a>
              </div>
            </div>
            <div className="aspect-video md:aspect-auto">
              <img
                src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?auto=format&fit=crop&q=80"
                alt="M-Thrift store interior with clothing racks"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProcessSection;
