
import { Users, ShoppingBag, MapPin } from "lucide-react";

const impactData = [
  {
    icon: <Users size={40} className="text-primary" />,
    metric: "5,000+",
    description: "Happy customers served"
  },
  {
    icon: <ShoppingBag size={40} className="text-primary" />,
    metric: "15,000+",
    description: "Quality items sold"
  },
  {
    icon: <MapPin size={40} className="text-primary" />,
    metric: "3",
    description: "Locations across Nairobi"
  }
];

const ImpactSection = () => {
  return (
    <section className="section bg-gray-50">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-medium mb-4">Our Community Impact</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          M-Thrift is more than just a store - we're building a community of fashion lovers who appreciate quality and value.
          Here's how we're making a difference in Kenya.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {impactData.map((item, index) => (
          <div 
            key={index} 
            className="bg-white p-8 rounded-lg text-center shadow-sm"
          >
            <div className="mb-4 flex justify-center">
              {item.icon}
            </div>
            <h3 className="text-3xl font-medium mb-2">{item.metric}</h3>
            <p className="text-muted-foreground">{item.description}</p>
          </div>
        ))}
      </div>
      
      <div className="mt-16 bg-primary/10 p-8 md:p-12 rounded-lg">
        <div className="flex flex-col md:flex-row gap-8 items-center">
          <div className="md:w-2/3">
            <h3 className="text-2xl md:text-3xl font-medium mb-4">
              Why Choose M-Thrift?
            </h3>
            <p className="mb-4">
              At M-Thrift, we believe everyone deserves access to quality fashion at affordable prices.
              Our carefully curated collection features the best second-hand clothing from around the world,
              bringing you style without the premium price tag.
            </p>
            <p>
              When you shop with us, you're not just getting great deals—you're supporting local employment
              and helping make fashion accessible to all Kenyans.
            </p>
          </div>
          <div className="md:w-1/3">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h4 className="font-medium mb-3">What we offer:</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  <span>Quality guaranteed items</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  <span>Prices up to 80% off retail</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  <span>New arrivals every week</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-primary">✓</span>
                  <span>M-Pesa payment accepted</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ImpactSection;
