import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import { Link } from "react-router-dom";

const categories = [
  {
    id: 1,
    name: "Men's Clothing",
    description: "Shirts, trousers, jackets, and more",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80",
    itemCount: 150
  },
  {
    id: 2,
    name: "Women's Clothing",
    description: "Dresses, blouses, skirts, and accessories",
    image: "https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?auto=format&fit=crop&q=80",
    itemCount: 200
  },
  {
    id: 3,
    name: "Kids' Clothing",
    description: "Clothes for boys and girls of all ages",
    image: "https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?auto=format&fit=crop&q=80",
    itemCount: 80
  },
  {
    id: 4,
    name: "Shoes & Footwear",
    description: "Sneakers, formal shoes, sandals",
    image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?auto=format&fit=crop&q=80",
    itemCount: 120
  },
  {
    id: 5,
    name: "Bags & Accessories",
    description: "Handbags, wallets, belts, jewelry",
    image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?auto=format&fit=crop&q=80",
    itemCount: 90
  },
  {
    id: 6,
    name: "Outerwear",
    description: "Jackets, coats, sweaters",
    image: "https://images.unsplash.com/photo-1551537482-f2075a1d41f2?auto=format&fit=crop&q=80",
    itemCount: 60
  }
];

const Categories = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="pt-24 pb-16 px-6 md:px-10 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-medium mb-4">Shop by Category</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Browse our organized collection of quality thrift items. Find exactly what you're looking for in each category.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Link key={category.id} to={`/shop/${category.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}`}>
              <Card className="overflow-hidden border-0 shadow-sm group hover:shadow-md transition-all duration-300 cursor-pointer">
                <div className="aspect-[4/3] relative overflow-hidden">
                  <img 
                    src={category.image} 
                    alt={category.name}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-semibold mb-1">{category.name}</h3>
                    <p className="text-sm opacity-90">{category.itemCount} items available</p>
                  </div>
                </div>
                <CardContent className="pt-4">
                  <p className="text-muted-foreground text-sm">{category.description}</p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
        
        {/* Special Offers Section */}
        <div className="mt-16 bg-primary/10 rounded-lg p-8">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-4">Weekly Special Offers</h2>
            <p className="text-muted-foreground mb-6">
              Don't miss out on our rotating weekly deals! Check back every Monday for new discounts.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-primary">Monday Special</h3>
                <p className="text-sm">20% off all Men's Shirts</p>
              </div>
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-primary">Wednesday Deal</h3>
                <p className="text-sm">Buy 2 Get 1 Free on Kids' Items</p>
              </div>
              <div className="bg-white p-4 rounded-lg">
                <h3 className="font-semibold text-primary">Friday Offer</h3>
                <p className="text-sm">30% off all Accessories</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Categories;
