
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 30 8% 12%;

    --card: 0 0% 100%;
    --card-foreground: 30 8% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 30 8% 12%;

    --primary: 2 84% 60%;
    --primary-foreground: 0 0% 98%;

    --secondary: 142 71% 45%;
    --secondary-foreground: 0 0% 98%;

    --muted: 30 10% 92%;
    --muted-foreground: 30 8% 40%;

    --accent: 30 40% 90%;
    --accent-foreground: 30 6% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 15% 85%;
    --input: 30 15% 85%;
    --ring: 2 84% 60%;

    --radius: 0.5rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, .serif {
    @apply font-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 py-3 px-6 rounded-md font-medium transition-colors;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 py-3 px-6 rounded-md font-medium transition-colors;
  }

  .btn-outline {
    @apply border border-primary text-primary hover:bg-primary/10 py-3 px-6 rounded-md font-medium transition-colors;
  }

  .section {
    @apply py-16 md:py-24 px-6 md:px-10 max-w-7xl mx-auto;
  }
}
