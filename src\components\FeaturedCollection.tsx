
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  image: string;
}

const featuredProducts: Product[] = [
  {
    id: 1,
    name: "Classic Denim Jacket",
    category: "Men's Outerwear",
    price: 1200,
    image: "https://images.unsplash.com/photo-1551537482-f2075a1d41f2?auto=format&fit=crop&q=80"
  },
  {
    id: 2,
    name: "Cotton Casual Shirt",
    category: "Men's Shirts",
    price: 800,
    image: "https://images.unsplash.com/photo-1591369822096-ffd140ec948f?auto=format&fit=crop&q=80"
  },
  {
    id: 3,
    name: "Vintage Midi Skirt",
    category: "Women's Bottoms",
    price: 600,
    image: "https://images.unsplash.com/photo-1551163943-3f6c855be1c4?auto=format&fit=crop&q=80"
  },
  {
    id: 4,
    name: "Floral Summer Dress",
    category: "Women's Dresses",
    price: 1500,
    image: "https://images.unsplash.com/photo-1539008835657-9e8e9680c956?auto=format&fit=crop&q=80"
  },
];

const FeaturedCollection = () => {
  return (
    <section id="featured" className="section">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-medium mb-4">Featured Items</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Check out our handpicked selection of quality thrift clothing. Fresh arrivals every week with unbeatable prices!
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {featuredProducts.map((product) => (
          <Card key={product.id} className="overflow-hidden border-0 shadow-sm group">
            <div className="aspect-[3/4] relative overflow-hidden">
              <img 
                src={product.image} 
                alt={product.name}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-foreground/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="absolute bottom-4 left-4 right-4">
                  <Button asChild variant="secondary" className="w-full">
                    <Link to={`/shop/product/${product.id}`}>View Details</Link>
                  </Button>
                </div>
              </div>
            </div>
            <CardContent className="pt-4">
              <p className="text-xs text-muted-foreground mb-1">{product.category}</p>
              <h3 className="font-medium mb-1">{product.name}</h3>
              <p className="font-medium">KES {product.price.toLocaleString()}</p>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="mt-12 text-center">
        <Button asChild size="lg">
          <Link to="/shop">View All Products</Link>
        </Button>
      </div>
    </section>
  );
};

export default FeaturedCollection;
