// React Query hooks for M-Thrift API calls
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { productApi, cartApi, orderApi, authApi, paymentApi, contactApi } from '@/lib/api';

// Product hooks
export const useProducts = (params?: { category?: string; page?: number; limit?: number }) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => productApi.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => productApi.getProduct(id),
    enabled: !!id,
  });
};

export const useSearchProducts = (query: string) => {
  return useQuery({
    queryKey: ['products', 'search', query],
    queryFn: () => productApi.searchProducts(query),
    enabled: !!query && query.length > 2,
  });
};

// Cart hooks
export const useCart = () => {
  return useQuery({
    queryKey: ['cart'],
    queryFn: cartApi.getCart,
  });
};

export const useAddToCart = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ productId, quantity }: { productId: string; quantity: number }) =>
      cartApi.addToCart(productId, quantity),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
    },
  });
};

export const useUpdateCartItem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ itemId, quantity }: { itemId: string; quantity: number }) =>
      cartApi.updateCartItem(itemId, quantity),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
    },
  });
};

export const useRemoveFromCart = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (itemId: string) => cartApi.removeFromCart(itemId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
    },
  });
};

// Order hooks
export const useOrders = () => {
  return useQuery({
    queryKey: ['orders'],
    queryFn: orderApi.getOrders,
  });
};

export const useOrder = (id: string) => {
  return useQuery({
    queryKey: ['order', id],
    queryFn: () => orderApi.getOrder(id),
    enabled: !!id,
  });
};

export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (orderData: any) => orderApi.createOrder(orderData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['cart'] });
    },
  });
};

// Auth hooks
export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      authApi.login(email, password),
    onSuccess: (data) => {
      localStorage.setItem('auth_token', data.token);
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
  });
};

export const useRegister = () => {
  return useMutation({
    mutationFn: (userData: any) => authApi.register(userData),
  });
};

export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: authApi.getProfile,
    enabled: !!localStorage.getItem('auth_token'),
  });
};

// Payment hooks
export const useInitiateMpesaPayment = () => {
  return useMutation({
    mutationFn: ({ phoneNumber, amount, orderId }: { 
      phoneNumber: string; 
      amount: number; 
      orderId: string 
    }) => paymentApi.initiateMpesaPayment(phoneNumber, amount, orderId),
  });
};

export const usePaymentStatus = (transactionId: string) => {
  return useQuery({
    queryKey: ['payment', 'status', transactionId],
    queryFn: () => paymentApi.checkPaymentStatus(transactionId),
    enabled: !!transactionId,
    refetchInterval: 5000, // Check every 5 seconds
  });
};

// Contact hooks
export const useSendMessage = () => {
  return useMutation({
    mutationFn: (messageData: any) => contactApi.sendMessage(messageData),
  });
};

export const useSubscribeNewsletter = () => {
  return useMutation({
    mutationFn: (email: string) => contactApi.subscribeNewsletter(email),
  });
};

// Example usage in components:
/*
// In a product listing component:
const ProductList = () => {
  const { data: products, isLoading, error } = useProducts({ category: 'men' });
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading products</div>;
  
  return (
    <div>
      {products?.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};

// In a cart component:
const AddToCartButton = ({ productId }: { productId: string }) => {
  const addToCart = useAddToCart();
  
  const handleAddToCart = () => {
    addToCart.mutate({ productId, quantity: 1 });
  };
  
  return (
    <button 
      onClick={handleAddToCart}
      disabled={addToCart.isPending}
    >
      {addToCart.isPending ? 'Adding...' : 'Add to Cart'}
    </button>
  );
};

// In a contact form:
const ContactForm = () => {
  const sendMessage = useSendMessage();
  
  const handleSubmit = (formData: any) => {
    sendMessage.mutate(formData, {
      onSuccess: () => {
        alert('Message sent successfully!');
      },
      onError: () => {
        alert('Failed to send message');
      }
    });
  };
  
  return <form onSubmit={handleSubmit}>...</form>;
};
*/
