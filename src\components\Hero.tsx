
import { ArrowDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const Hero = () => {
  const scrollToNextSection = () => {
    const featuredSection = document.getElementById('featured');
    if (featuredSection) {
      featuredSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="relative h-screen overflow-hidden">
      {/* Background Image with Gradient Overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?auto=format&fit=crop&q=80')`,
          backgroundPosition: '50% 40%'
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
      </div>
      
      {/* Content */}
      <div className="relative h-full flex items-center">
        <div className="section text-white max-w-3xl">
          <h1 className="text-4xl md:text-6xl font-medium leading-tight mb-6 animate-fade-in">
            Quality Thrift Fashion <span className="block">At Unbeatable Prices</span>
          </h1>
          <p className="text-lg md:text-xl mb-8 max-w-xl opacity-90 animate-fade-in" style={{animationDelay: "0.2s"}}>
            Discover Kenya's premier thrift store with carefully curated second-hand clothing. From vintage finds to modern styles - all at prices that won't break the bank.
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-in" style={{animationDelay: "0.4s"}}>
            <Button asChild size="lg" className="bg-primary text-white hover:bg-primary/90">
              <Link to="/shop">Shop Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white/20">
              <Link to="/about">About M-Thrift</Link>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Scroll Down Indicator */}
      <button 
        onClick={scrollToNextSection}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white flex flex-col items-center opacity-80 hover:opacity-100 transition-opacity"
        aria-label="Scroll down"
      >
        <span className="text-sm mb-2">Explore</span>
        <ArrowDown size={20} className="animate-bounce" />
      </button>
    </div>
  );
};

export default Hero;
