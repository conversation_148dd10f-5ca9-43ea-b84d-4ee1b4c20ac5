
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import { Users, Target, Heart, Award } from "lucide-react";

const About = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="pt-24 pb-16 px-6 md:px-10 max-w-7xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-3xl md:text-4xl font-medium mb-4">About M-Thrift</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            Kenya's premier thrift store chain, making quality fashion accessible to everyone while supporting local communities.
          </p>
        </div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-2xl font-semibold mb-6">Our Story</h2>
            <div className="space-y-4 text-muted-foreground">
              <p>
                M-Thrift was founded in 2020 with a simple mission: to make quality fashion accessible to all Kenyans.
                What started as a small shop in Nairobi's CBD has grown into a trusted chain of three stores across the city.
              </p>
              <p>
                We believe that everyone deserves to look and feel their best, regardless of their budget. That's why we
                carefully curate our collection of second-hand clothing, ensuring every item meets our high standards for
                quality and style.
              </p>
              <p>
                Today, M-Thrift serves thousands of customers monthly, providing them with fashionable clothing at
                unbeatable prices while creating employment opportunities in our local communities.
              </p>
            </div>
          </div>
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?auto=format&fit=crop&q=80"
              alt="M-Thrift store interior"
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold text-center mb-8">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6">
              <CardContent className="pt-6">
                <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Community First</h3>
                <p className="text-sm text-muted-foreground">
                  Supporting local communities through employment and affordable fashion access.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6">
              <CardContent className="pt-6">
                <Target className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Quality Focus</h3>
                <p className="text-sm text-muted-foreground">
                  Every item is carefully inspected to ensure it meets our high quality standards.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6">
              <CardContent className="pt-6">
                <Heart className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Customer Care</h3>
                <p className="text-sm text-muted-foreground">
                  Providing excellent service and helping customers find exactly what they need.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center p-6">
              <CardContent className="pt-6">
                <Award className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">Fair Pricing</h3>
                <p className="text-sm text-muted-foreground">
                  Offering the best prices in the market without compromising on quality.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Team Section */}
        <div className="bg-gray-50 rounded-lg p-8 md:p-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-semibold mb-4">Meet Our Team</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our dedicated team works hard every day to bring you the best thrift shopping experience in Kenya.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-24 h-24 bg-primary/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Users className="h-12 w-12 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Sarah Wanjiku</h3>
              <p className="text-sm text-muted-foreground mb-2">Founder & CEO</p>
              <p className="text-xs text-muted-foreground">
                Passionate about making fashion accessible to all Kenyans.
              </p>
            </div>

            <div className="text-center">
              <div className="w-24 h-24 bg-primary/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Target className="h-12 w-12 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">James Kiprotich</h3>
              <p className="text-sm text-muted-foreground mb-2">Operations Manager</p>
              <p className="text-xs text-muted-foreground">
                Ensures quality and efficiency across all our store locations.
              </p>
            </div>

            <div className="text-center">
              <div className="w-24 h-24 bg-primary/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Heart className="h-12 w-12 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Grace Akinyi</h3>
              <p className="text-sm text-muted-foreground mb-2">Customer Relations</p>
              <p className="text-xs text-muted-foreground">
                Dedicated to providing excellent customer service and support.
              </p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default About;
