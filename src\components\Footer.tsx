
import { Link } from "react-router-dom";
import { ShoppingBag, Mail } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

const Footer = () => {
  return (
    <footer className="bg-gray-100 pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-6 md:px-10">
        {/* Newsletter */}
        <div className="bg-primary/10 rounded-lg p-8 mb-12">
          <div className="flex flex-col md:flex-row gap-8 items-center justify-between">
            <div>
              <h3 className="text-2xl font-serif font-medium mb-2">Stay Updated with M-Thrift</h3>
              <p className="text-muted-foreground">Get notified about new arrivals, special offers, and exclusive deals.</p>
            </div>
            <div className="w-full md:w-auto flex flex-col sm:flex-row gap-3">
              <Input
                type="email"
                placeholder="Enter your email"
                className="bg-background min-w-[240px]"
              />
              <Button>Subscribe</Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-10 mb-12">
          {/* Brand */}
          <div className="md:col-span-1">
            <Link to="/" className="font-serif text-2xl font-bold flex items-center gap-2 text-primary">
              <ShoppingBag size={24} className="text-primary" />
              M-Thrift
            </Link>
            <p className="mt-4 text-muted-foreground">Kenya's premier thrift store offering quality second-hand clothing at unbeatable prices.</p>
          </div>

          {/* Links */}
          <div>
            <h4 className="font-medium text-lg mb-4">Shop</h4>
            <ul className="space-y-2">
              <li><Link to="/shop/men" className="hover:text-primary transition-colors">Men's Clothing</Link></li>
              <li><Link to="/shop/women" className="hover:text-primary transition-colors">Women's Clothing</Link></li>
              <li><Link to="/shop/kids" className="hover:text-primary transition-colors">Kids' Clothing</Link></li>
              <li><Link to="/shop/accessories" className="hover:text-primary transition-colors">Accessories</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-lg mb-4">Company</h4>
            <ul className="space-y-2">
              <li><Link to="/about" className="hover:text-primary transition-colors">About Us</Link></li>
              <li><Link to="/locations" className="hover:text-primary transition-colors">Store Locations</Link></li>
              <li><Link to="/careers" className="hover:text-primary transition-colors">Careers</Link></li>
              <li><Link to="/contact" className="hover:text-primary transition-colors">Contact</Link></li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-lg mb-4">Support</h4>
            <ul className="space-y-2">
              <li><Link to="/help" className="hover:text-primary transition-colors">Help Center</Link></li>
              <li><Link to="/shipping" className="hover:text-primary transition-colors">Shipping Info</Link></li>
              <li><Link to="/returns" className="hover:text-primary transition-colors">Returns</Link></li>
              <li><Link to="/payment" className="hover:text-primary transition-colors">Payment Options</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-gray-200 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground mb-4 md:mb-0">
            © 2025 M-Thrift Kenya. All rights reserved.
          </p>
          <div className="flex space-x-6">
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors">Instagram</a>
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors">Facebook</a>
            <a href="#" className="text-muted-foreground hover:text-primary transition-colors">WhatsApp</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
