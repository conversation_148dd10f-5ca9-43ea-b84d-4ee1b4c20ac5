
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  originalPrice?: number;
  image: string;
  condition: string;
}

const products: Product[] = [
  {
    id: 1,
    name: "Classic Denim Jacket",
    category: "Men's Outerwear",
    price: 1200,
    originalPrice: 3500,
    image: "https://images.unsplash.com/photo-1551537482-f2075a1d41f2?auto=format&fit=crop&q=80",
    condition: "Excellent"
  },
  {
    id: 2,
    name: "Cotton Casual Shirt",
    category: "Men's Shirts",
    price: 800,
    originalPrice: 2000,
    image: "https://images.unsplash.com/photo-1591369822096-ffd140ec948f?auto=format&fit=crop&q=80",
    condition: "Very Good"
  },
  {
    id: 3,
    name: "Vintage Midi Skirt",
    category: "Women's Bottoms",
    price: 600,
    originalPrice: 1800,
    image: "https://images.unsplash.com/photo-1551163943-3f6c855be1c4?auto=format&fit=crop&q=80",
    condition: "Good"
  },
  {
    id: 4,
    name: "Floral Summer Dress",
    category: "Women's Dresses",
    price: 1500,
    originalPrice: 4000,
    image: "https://images.unsplash.com/photo-1539008835657-9e8e9680c956?auto=format&fit=crop&q=80",
    condition: "Excellent"
  },
  {
    id: 5,
    name: "Leather Handbag",
    category: "Accessories",
    price: 2000,
    originalPrice: 6000,
    image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?auto=format&fit=crop&q=80",
    condition: "Very Good"
  },
  {
    id: 6,
    name: "Wool Blazer",
    category: "Women's Outerwear",
    price: 1800,
    originalPrice: 5000,
    image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?auto=format&fit=crop&q=80",
    condition: "Excellent"
  }
];

const Shop = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="pt-24 pb-16 px-6 md:px-10 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-medium mb-4">Shop M-Thrift</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Discover amazing deals on quality second-hand clothing. All items are carefully inspected and priced to save you money.
          </p>
        </div>

        {/* Categories Filter */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          <Button variant="outline" size="sm">All Items</Button>
          <Button variant="outline" size="sm">Men's Clothing</Button>
          <Button variant="outline" size="sm">Women's Clothing</Button>
          <Button variant="outline" size="sm">Accessories</Button>
          <Button variant="outline" size="sm">Outerwear</Button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <Card key={product.id} className="overflow-hidden border-0 shadow-sm group hover:shadow-md transition-shadow">
              <div className="aspect-[3/4] relative overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute top-2 left-2">
                  <Badge variant="secondary" className="bg-white/90 text-xs">
                    {product.condition}
                  </Badge>
                </div>
                {product.originalPrice && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="destructive" className="text-xs">
                      {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                    </Badge>
                  </div>
                )}
              </div>
              <CardContent className="pt-4">
                <p className="text-xs text-muted-foreground mb-1">{product.category}</p>
                <h3 className="font-medium mb-2">{product.name}</h3>
                <div className="flex items-center gap-2 mb-3">
                  <span className="font-bold text-primary">KES {product.price.toLocaleString()}</span>
                  {product.originalPrice && (
                    <span className="text-sm text-muted-foreground line-through">
                      KES {product.originalPrice.toLocaleString()}
                    </span>
                  )}
                </div>
                <Button className="w-full" size="sm">
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="mt-12 text-center">
          <Button variant="outline" size="lg">
            Load More Items
          </Button>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Shop;
