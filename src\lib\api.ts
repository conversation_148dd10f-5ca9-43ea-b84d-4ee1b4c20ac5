// Example API service layer for M-Thrift backend calls

const API_BASE_URL = process.env.VITE_API_URL || 'http://localhost:3001/api';

// Generic API client
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export const apiClient = new ApiClient(API_BASE_URL);

// Product-related API calls
export const productApi = {
  getProducts: (params?: { category?: string; page?: number; limit?: number }) => 
    apiClient.get(`/products?${new URLSearchParams(params as any)}`),
  
  getProduct: (id: string) => 
    apiClient.get(`/products/${id}`),
  
  searchProducts: (query: string) => 
    apiClient.get(`/products/search?q=${encodeURIComponent(query)}`),
};

// Cart-related API calls
export const cartApi = {
  getCart: () => apiClient.get('/cart'),
  addToCart: (productId: string, quantity: number) => 
    apiClient.post('/cart/items', { productId, quantity }),
  updateCartItem: (itemId: string, quantity: number) => 
    apiClient.put(`/cart/items/${itemId}`, { quantity }),
  removeFromCart: (itemId: string) => 
    apiClient.delete(`/cart/items/${itemId}`),
  clearCart: () => apiClient.delete('/cart'),
};

// Order-related API calls
export const orderApi = {
  createOrder: (orderData: any) => 
    apiClient.post('/orders', orderData),
  getOrders: () => 
    apiClient.get('/orders'),
  getOrder: (id: string) => 
    apiClient.get(`/orders/${id}`),
  updateOrderStatus: (id: string, status: string) => 
    apiClient.put(`/orders/${id}/status`, { status }),
};

// User/Auth API calls
export const authApi = {
  login: (email: string, password: string) => 
    apiClient.post('/auth/login', { email, password }),
  register: (userData: any) => 
    apiClient.post('/auth/register', userData),
  logout: () => apiClient.post('/auth/logout', {}),
  getProfile: () => apiClient.get('/auth/profile'),
  updateProfile: (userData: any) => 
    apiClient.put('/auth/profile', userData),
};

// M-Pesa payment API calls
export const paymentApi = {
  initiateMpesaPayment: (phoneNumber: string, amount: number, orderId: string) => 
    apiClient.post('/payments/mpesa/initiate', { phoneNumber, amount, orderId }),
  checkPaymentStatus: (transactionId: string) => 
    apiClient.get(`/payments/status/${transactionId}`),
};

// Contact/Support API calls
export const contactApi = {
  sendMessage: (messageData: any) => 
    apiClient.post('/contact', messageData),
  subscribeNewsletter: (email: string) => 
    apiClient.post('/newsletter/subscribe', { email }),
};
