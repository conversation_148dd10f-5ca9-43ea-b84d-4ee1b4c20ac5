
import { useState, useEffect } from "react";
import { Menu, X, ShoppingBag } from "lucide-react";
import { Link } from "react-router-dom";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-background/95 backdrop-blur-md shadow-sm' : 'bg-transparent'}`}>
      <div className="max-w-7xl mx-auto px-6 md:px-10">
        <div className="flex justify-between items-center py-4 md:py-6">
          {/* Logo */}
          <Link to="/" className="font-serif text-2xl font-bold text-primary">M-Thrift</Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-8 items-center">
            <Link to="/" className="hover:text-primary transition-colors">Home</Link>
            <Link to="/shop" className="hover:text-primary transition-colors">Shop</Link>
            <Link to="/about" className="hover:text-primary transition-colors">About Us</Link>
            <Link to="/categories" className="hover:text-primary transition-colors">Categories</Link>
            <Link to="/contact" className="hover:text-primary transition-colors">Contact</Link>
            <Link to="/cart" aria-label="Shopping cart" className="hover:text-primary transition-colors">
              <ShoppingBag size={20} />
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-background absolute w-full py-5 shadow-lg animate-fade-in">
          <div className="flex flex-col space-y-4 px-6 py-3">
            <Link to="/" onClick={() => setIsMenuOpen(false)} className="hover:text-primary transition-colors py-1">Home</Link>
            <Link to="/shop" onClick={() => setIsMenuOpen(false)} className="hover:text-primary transition-colors py-1">Shop</Link>
            <Link to="/about" onClick={() => setIsMenuOpen(false)} className="hover:text-primary transition-colors py-1">About Us</Link>
            <Link to="/categories" onClick={() => setIsMenuOpen(false)} className="hover:text-primary transition-colors py-1">Categories</Link>
            <Link to="/contact" onClick={() => setIsMenuOpen(false)} className="hover:text-primary transition-colors py-1">Contact</Link>
            <Link to="/cart" onClick={() => setIsMenuOpen(false)} className="hover:text-primary transition-colors py-1 flex items-center gap-2">
              <ShoppingBag size={20} /> Cart
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
